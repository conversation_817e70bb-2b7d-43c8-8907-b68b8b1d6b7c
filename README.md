# 刷宝量化交易平台 (ShuaBao Quantitative Trading Platform)

## 项目概述

这是一个现代化的、基于Web的量化交易平台，融合了多智能体强化学习（MARL）技术和业界最佳实践。本平台深度借鉴了多个领先量化平台的优秀设计理念，旨在打造一个统一的、AI原生的"量化工厂"。

## 设计理念融合

本项目深度借鉴了以下业界领先平台的优秀架构模式：

- **OpenBB Platform**：模块化数据路由架构和TET（Transform-Extract-Transform）管道模式
- **QuantConnect LEAN**：Algorithm Framework分层设计和关注点分离原则  
- **VeighNa (vnpy)**：事件驱动反应器模式和高频交易优化
- **FreqTrade**：三层架构和风险管理集成
- **Zipline**：事件驱动回测引擎和数据对齐机制

## 核心特性

### 1. 统一数据路由层
- 参考OpenBB的Provider-Router架构
- TET管道模式：Transform-Extract-Transform
- 统一的数据访问接口，支持多数据源

### 2. 事件驱动架构
- 借鉴VeighNa的反应器模式
- 微秒级延迟的高频交易支持
- 异步消息处理和事件分发

### 3. Algorithm Framework
- 采用QuantConnect的分层设计
- Universe Selection → Alpha → Portfolio Construction → Risk Management → Execution
- 关注点分离和模块复用

### 4. 多智能体强化学习
- 将金融市场建模为多智能体博弈
- 支持协作式、竞争式、混合式智能体
- 基于PettingZoo和Ray RLlib的训练框架

### 5. 可插拔回测引擎
- 支持Backtrader、Zipline、VeighNa等多种引擎
- 针对不同场景的引擎选择指导
- 自研MARL优化引擎

## 技术栈

| 组件 | 技术选择 | 业界参考 |
|------|----------|----------|
| 前端框架 | Vue 3 + Pinia | OpenBB Terminal |
| 后端API | FastAPI | OpenBB Platform, VeighNa |
| 数据路由 | 自研Provider-Router | OpenBB Platform |
| 事件引擎 | 自研Reactor Pattern | VeighNa, LEAN Engine |
| 时序数据库 | TimescaleDB | 企业级平台 |
| 缓存/消息 | Redis | FreqTrade, VeighNa |
| 回测引擎 | Backtrader (可插拔) | QuantConnect LEAN, Zipline |
| MARL框架 | Ray RLlib + PettingZoo | 学术研究平台 |
| 容器化 | Docker + Kubernetes | 云原生平台 |

## 项目结构

```
shuabao-trade/
├── docs/                    # 文档目录
│   └── 设计文档.md          # 详细设计文档
├── backend/                 # 后端服务
│   ├── api/                # FastAPI应用
│   ├── data/               # 数据服务
│   ├── backtest/           # 回测引擎
│   ├── marl/               # MARL训练平台
│   └── common/             # 公共组件
├── frontend/               # 前端应用
│   ├── src/                # Vue 3源码
│   └── public/             # 静态资源
├── data/                   # 数据存储
├── docker/                 # Docker配置
└── k8s/                    # Kubernetes配置
```

## 快速开始

### 环境要求
- Python 3.9+
- Node.js 16+
- Docker & Docker Compose
- PostgreSQL 14+
- Redis 6+

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/your-repo/shuabao-trade.git
cd shuabao-trade
```

2. **后端环境设置**
```bash
cd backend
pip install -r requirements.txt
```

3. **前端环境设置**
```bash
cd frontend
npm install
```

4. **数据库初始化**
```bash
# 启动PostgreSQL和Redis
docker-compose up -d postgres redis

# 运行数据库迁移
python manage.py migrate
```

5. **启动服务**
```bash
# 后端服务
uvicorn api.main:app --reload

# 前端服务
npm run dev
```

## 开发指南

### 数据提供商集成
参考OpenBB Platform的Provider模式，新增数据源只需实现标准接口：

```python
class DataProvider(ABC):
    @abstractmethod
    def fetch_data(self, symbol: str, start: datetime, end: datetime) -> pd.DataFrame:
        pass
```

### 策略开发
支持多种策略开发模式：

1. **传统策略**：基于Backtrader的事件驱动策略
2. **因子策略**：基于Zipline Pipeline的因子研究
3. **MARL策略**：基于PettingZoo的多智能体策略

### 回测引擎选择
- **Backtrader**：传统策略，成熟稳定
- **Zipline**：因子研究，数据处理能力强
- **VeighNa**：高频交易，延迟更低
- **自研引擎**：MARL策略，支持多智能体交互

## 贡献指南

1. Fork项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

## 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目主页：[GitHub Repository](https://github.com/your-repo/shuabao-trade)
- 问题反馈：[Issues](https://github.com/your-repo/shuabao-trade/issues)
- 讨论交流：[Discussions](https://github.com/your-repo/shuabao-trade/discussions)

## 致谢

感谢以下开源项目的启发和贡献：
- OpenBB Platform
- QuantConnect LEAN
- VeighNa (vnpy)
- FreqTrade
- Zipline
- Backtrader
- Ray RLlib
- PettingZoo
